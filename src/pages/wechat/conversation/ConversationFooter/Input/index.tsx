import {
  useState,
  type Dispatch,
  type SetStateAction,
  memo,
  useCallback,
  useMemo,
} from 'react'
import { isMobileOnly } from 'react-device-detect'
import { useHistory } from 'react-router-dom'
import { useConversationAPI } from '../../context'
import { MockReplyService } from './mockReplyService'

type Props = {
  showEmojiPanel?: boolean
  setShowEmojiPanel?: Dispatch<SetStateAction<boolean>>
}

const Input = ({ showEmojiPanel, setShowEmojiPanel }: Props) => {
  const [inputValue, setInputValue] = useState('')
  const { sendTextMessage, sendNotification, updateNotification, demoService } =
    useConversationAPI()

  // 创建模拟回复服务实例
  const mockReplyService = useMemo(() => {
    if (!demoService) return null
    return new MockReplyService(
      demoService,
      sendNotification,
      updateNotification,
    )
  }, [demoService, sendNotification, updateNotification])

  const history = useHistory()

  const handleSend = () => {
    let content = inputValue.trim()
    if (content) {
      // 模拟回复
      if (!mockReplyService?.generateMockReply(content, history)) {
        // 发送用户消息
        sendTextMessage(content)
        mockReplyService.sendNotificationReply()
      }

      // 清空输入框
      setInputValue('')
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleSend()
    }
  }

  const handleFocus = () => {
    if (isMobileOnly && showEmojiPanel) {
      setShowEmojiPanel?.(false)
    }
  }

  return (
    <div className='min-w-0 flex-1'>
      <input
        id='conversation-input'
        type='text'
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        className='w-full rounded bg-white px-2 py-1 caret-wechatBrand-3 focus:outline-none'
        placeholder='输入消息...'
        enterKeyHint='send'
      />
    </div>
  )
}

export default memo(Input)
