import { useState } from 'react'
import { performUnifiedLogout } from '@/tina/lib/auth.ts'
import {
  IonAvatar,
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonPage,
  IonTitle,
  IonToggle,
  IonToolbar,
} from '@ionic/react'
import {
  chatbubbleOutline,
  chevronForwardOutline,
  colorPaletteOutline,
  createOutline,
  helpCircleOutline,
  informationCircleOutline,
  logOutOutline,
  micOutline,
  notificationsOutline,
  shieldCheckmarkOutline,
  timeOutline,
} from 'ionicons/icons'
import BetaInfoDialog from '@/components/BetaInfoDialog'
import WechatDialog from '../../components/WechatDialog'
import { HeaderBackButton } from '../../components/ui/back-button'
import useAuth from '../../tina/stores/authStore'
import './settings.css'

/**
 * 移动端设置页面组件
 * 包含用户信息、通用设置、隐私安全、关于帮助等功能
 */
export default function MobileSettings() {
  const auth = useAuth()

  const [settings, setSettings] = useState({
    notifications: true,
    voiceInput: true,
    darkMode: false,
  })
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [showBetaInfo, setShowBetaInfo] = useState(false)

  // 从 authStore 获取用户信息
  const user = auth.user
  const userName = user?.displayName || user?.name || '用户'
  const name = user?.name || ''
  const userAvatar = userName.charAt(0).toUpperCase()

  const handleToggleChange = (key: keyof typeof settings) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  const handleLogout = () => {
    setShowLogoutDialog(true)
  }

  const handleLogoutConfirm = async () => {
    console.log('🚪 [Settings] 开始注销流程')
    setIsLoggingOut(true)

    try {
      // 使用统一登出逻辑
      await performUnifiedLogout('用户手动登出')
    } catch (error) {
      console.error('🚪 [Settings] 注销失败:', error)
      // 即使统一登出失败，也要确保页面状态正确
    }
    // 注意：不要在这里设置 setIsLoggingOut(false)，因为页面即将跳转
  }

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false)
  }

  const handleEditProfile = () => {
    // TODO: 实现编辑个人资料功能
    // console.log('编辑个人资料')
  }

  const handleCloseBetaInfo = () => {
    setShowBetaInfo(false)
  }

  const handleNavigationClick = (title: string) => {
    if (title === '关于 Tina Chat') {
      setShowBetaInfo(true)
    } else {
      // TODO: 实现其他导航功能
      // console.log(`Navigate to ${title}`)
    }
  }

  // 定义设置项类型
  interface SettingItem {
    icon: string
    title: string
    subtitle: string
    type: 'toggle' | 'navigation'
    key?: keyof typeof settings
    color: string
    badge?: string
  }

  interface SettingGroup {
    group: string
    items: SettingItem[]
  }

  const settingsItems: SettingGroup[] = [
    {
      group: '通用设置',
      items: [
        {
          icon: notificationsOutline,
          title: '通知设置',
          subtitle: '管理推送通知和提醒',
          type: 'toggle',
          key: 'notifications',
          color: 'primary',
        },
        {
          icon: micOutline,
          title: '语音输入',
          subtitle: '启用语音识别功能',
          type: 'toggle',
          key: 'voiceInput',
          color: 'warning',
        },
        {
          icon: colorPaletteOutline,
          title: '主题设置',
          subtitle: '选择应用主题和外观',
          type: 'navigation',
          color: 'secondary',
        },
      ],
    },
    {
      group: '隐私与安全',
      items: [
        {
          icon: shieldCheckmarkOutline,
          title: '数据与隐私',
          subtitle: '管理数据使用和隐私设置',
          type: 'navigation',
          color: 'success',
        },
        {
          icon: timeOutline,
          title: '聊天记录',
          subtitle: '管理和清理聊天记录',
          type: 'navigation',
          color: 'warning',
        },
      ],
    },
    {
      group: '关于与帮助',
      items: [
        {
          icon: helpCircleOutline,
          title: '帮助中心',
          subtitle: '常见问题和使用指南',
          type: 'navigation',
          color: 'tertiary',
        },
        {
          icon: chatbubbleOutline,
          title: '反馈建议',
          subtitle: '向我们反馈问题和建议',
          type: 'navigation',
          color: 'danger',
        },
        {
          icon: informationCircleOutline,
          title: '关于 Tina Chat',
          subtitle: import.meta.env.MODE,
          type: 'navigation',
          color: 'medium',
          badge: 'v0.0.1 0716',
        },
      ],
    },
  ]

  return (
    <IonPage className={'m-auto sm:max-w-md'}>
      <IonHeader>
        <IonToolbar
          style={{
            '--background': '#FFFFFF',
            '--border-width': '0 0 1px 0',
            '--border-color': '#E5E5E5',
          }}
        >
          <IonButtons slot='start'>
            <HeaderBackButton defaultHref='/conversation/1' />
          </IonButtons>
          <IonTitle
            style={{ color: '#191919', fontSize: '17px', fontWeight: '600' }}
          >
            设置
          </IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent
        style={{
          '--background': '#F3F3F3',
        }}
      >
        {/* 个人信息卡片 */}
        <div
          style={{
            position: 'relative',
            margin: '12px 16px',
            borderRadius: '8px',
            backgroundColor: '#FFFFFF',
            padding: '16px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
            border: '1px solid #E5E5E5',
          }}
        >
          {/* 编辑按钮 - 右上角 */}
          <IonButton
            fill='clear'
            size='small'
            style={{
              position: 'absolute',
              right: '8px',
              top: '8px',
              '--color': '#8E8E93',
            }}
            onClick={handleEditProfile}
          >
            <IonIcon icon={createOutline} style={{ fontSize: '16px' }} />
          </IonButton>

          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* 用户头像 */}
            <IonAvatar style={{ width: '60px', height: '60px' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100%',
                  height: '100%',
                  borderRadius: '50%',
                  backgroundColor: '#07C160',
                  fontSize: '24px',
                  fontWeight: '500',
                  color: '#FFFFFF',
                }}
              >
                {userAvatar}
              </div>
            </IonAvatar>

            <div style={{ marginLeft: '16px', flex: 1 }}>
              <h3
                style={{
                  fontSize: '17px',
                  fontWeight: '600',
                  color: '#191919',
                  margin: '0 0 4px 0',
                }}
              >
                {userName}
              </h3>

              <div style={{ fontSize: '14px', color: '#8E8E93' }}>{name}</div>
            </div>
          </div>
        </div>

        {/* 设置列表 */}
        <div style={{ flex: 1, paddingBottom: '16px' }}>
          {settingsItems.map((group, groupIndex) => (
            <div key={groupIndex} style={{ marginTop: '12px' }}>
              {/* 分组标题 */}
              <div
                style={{
                  paddingLeft: '16px',
                  paddingRight: '16px',
                  paddingTop: '12px',
                  paddingBottom: '8px',
                }}
              >
                <h4
                  style={{
                    fontSize: '13px',
                    fontWeight: '400',
                    color: '#8E8E93',
                    margin: 0,
                    textTransform: 'none',
                  }}
                >
                  {group.group}
                </h4>
              </div>

              {/* 设置项容器 */}
              <div
                style={{
                  backgroundColor: '#FFFFFF',
                  margin: '0 16px',
                  borderRadius: '8px',
                  border: '1px solid #E5E5E5',
                  overflow: 'hidden',
                }}
              >
                {group.items.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '16px',
                      borderBottom:
                        itemIndex < group.items.length - 1
                          ? '1px solid #F3F3F3'
                          : 'none',
                      cursor:
                        item.type === 'navigation' ? 'pointer' : 'default',
                      transition: 'all 0.2s ease',
                    }}
                    onClick={
                      item.type === 'navigation'
                        ? () => handleNavigationClick(item.title)
                        : undefined
                    }
                    onMouseDown={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#F5F5F5'
                      }
                    }}
                    onMouseUp={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#FFFFFF'
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#FFFFFF'
                      }
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '28px',
                          height: '28px',
                          borderRadius: '6px',
                          backgroundColor:
                            item.color === 'primary'
                              ? '#E3F2FD'
                              : item.color === 'warning'
                                ? '#FFF3E0'
                                : item.color === 'secondary'
                                  ? '#F3E5F5'
                                  : item.color === 'success'
                                    ? '#E8F5E9'
                                    : item.color === 'tertiary'
                                      ? '#E0F2F1'
                                      : item.color === 'danger'
                                        ? '#FFEBEE'
                                        : item.color === 'medium'
                                          ? '#F3E5F5'
                                          : '#F5F5F5',
                          marginRight: '12px',
                        }}
                      >
                        <IonIcon
                          icon={item.icon}
                          style={{
                            fontSize: '16px',
                            color:
                              item.color === 'primary'
                                ? '#2196F3'
                                : item.color === 'warning'
                                  ? '#FF9500'
                                  : item.color === 'secondary'
                                    ? '#9C27B0'
                                    : item.color === 'success'
                                      ? '#07C160'
                                      : item.color === 'tertiary'
                                        ? '#009688'
                                        : item.color === 'danger'
                                          ? '#F44336'
                                          : item.color === 'medium'
                                            ? '#9C27B0'
                                            : '#8E8E93',
                          }}
                        />
                      </div>
                      <div style={{ textAlign: 'left' }}>
                        <p
                          style={{
                            fontSize: '16px',
                            fontWeight: '400',
                            color: '#191919',
                            margin: '0 0 2px 0',
                          }}
                        >
                          {item.title}
                        </p>
                        <p
                          style={{
                            fontSize: '13px',
                            color: '#8E8E93',
                            margin: 0,
                          }}
                        >
                          {item.subtitle}
                        </p>
                      </div>
                    </div>

                    {item.type === 'toggle' && item.key && (
                      <IonToggle
                        checked={settings[item.key as keyof typeof settings]}
                        onIonChange={() =>
                          handleToggleChange(item.key as keyof typeof settings)
                        }
                        style={{
                          '--background': '#E5E5E5',
                          '--background-checked': '#07C160',
                          '--handle-background': '#FFFFFF',
                          '--handle-background-checked': '#FFFFFF',
                        }}
                      />
                    )}

                    {item.type === 'navigation' && (
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        {item.badge && (
                          <span
                            style={{
                              fontSize: '13px',
                              color: '#8E8E93',
                              marginRight: '8px',
                            }}
                          >
                            {item.badge}
                          </span>
                        )}
                        <IonIcon
                          icon={chevronForwardOutline}
                          style={{ fontSize: '14px', color: '#C7C7CC' }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 退出登录 */}
        <div style={{ marginBottom: '16px', marginTop: '12px' }}>
          <div
            style={{
              backgroundColor: '#FFFFFF',
              margin: '0 16px',
              borderRadius: '8px',
              border: '1px solid #E5E5E5',
              overflow: 'hidden',
            }}
          >
            <button
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '16px',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
              }}
              onClick={handleLogout}
              onMouseDown={(e) => {
                e.currentTarget.style.backgroundColor = '#FFF5F5'
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <IonIcon
                  icon={logOutOutline}
                  style={{
                    fontSize: '18px',
                    color: '#FF3B30',
                    marginRight: '8px',
                  }}
                />
                <span
                  style={{
                    fontSize: '16px',
                    fontWeight: '400',
                    color: '#FF3B30',
                  }}
                >
                  退出登录
                </span>
              </div>
            </button>
          </div>
        </div>
      </IonContent>

      <WechatDialog
        isOpen={showLogoutDialog}
        title='确定注销当前用户？'
        content=' 注销不会导致数据丢失，但需要重新登录，注销完成后，将返回首页。'
        secondaryButtonText='取消'
        primaryButtonText='确定'
        onSecondaryClick={handleLogoutCancel}
        onPrimaryClick={handleLogoutConfirm}
        onClose={handleLogoutCancel}
        loading={isLoggingOut}
      />

      <BetaInfoDialog
        isOpen={showBetaInfo}
        onClose={handleCloseBetaInfo}
      />
    </IonPage>
  )
}
