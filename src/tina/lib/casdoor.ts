import { fetchGet, fetchPost, handleResponse } from '@/tina/lib/fetch.ts'
import { AuthUser } from '@/tina/stores/authStore.ts'

// 从环境变量中获取网关URL
export const GATEWAY_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:8380'
const redirectUri = `http://localhost:8380/api/v1/casdoor/callback`

// Casdoor配置接口
interface CasdoorConfig {
  casdoor_endpoint: string
  client_id: string
  organization: string
  application: string
}

// 全局配置缓存
let casdoorConfig: CasdoorConfig | null = null

/**
 * 获取 Casdoor 配置
 * 只获取一次，后续使用缓存
 */
async function getCasdoorConfig(): Promise<CasdoorConfig> {
  if (casdoorConfig) {
    return casdoorConfig
  }

  try {
    console.log('获取 Casdoor 配置', GATEWAY_URL)
    const response = await fetch(`${GATEWAY_URL}/api/v1/casdoor/user`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    console.log('获取 Casdoor 配置成功')

    casdoorConfig = await response.json()
    console.log('Casdoor 配置:', casdoorConfig)
    return casdoorConfig!
  } catch (error) {
    throw new Error(`获取 Casdoor 配置失败: ${error}`)
  }
}

// 注册请求载荷接口
interface SignupPayload {
  application: string
  organization: string
  username: string
  password: string
  confirm: string
  agreement: boolean
  email?: string
  displayName?: string
}

// 1. 注册
export async function casdoorSignup({
  username,
  password,
  email,
  displayName,
}: {
  username: string
  password: string
  email?: string
  displayName?: string
}) {
  // 获取动态配置
  const config = await getCasdoorConfig()

  // const signupUrl = `${config.casdoor_endpoint}/api/signup`
  const signupUrl = 'https://tina-test.bfbdata.com/casdoor/api/signup'
  const payload: SignupPayload = {
    application: config.application,
    organization: config.organization,
    username,
    password,
    confirm: password,
    agreement: true,
  }
  if (email) payload.email = email
  if (displayName) payload.displayName = displayName

  return await fetchPost(signupUrl, payload, {
    credentials: 'omit', // 明确指示浏览器不发送cookie
  })
}

// 2. 登录获取 code
export async function casdoorGetCode({
  username,
  password,
}: {
  username: string
  password: string
}) {
  // 获取动态配置
  const config = await getCasdoorConfig()

  // const signinUrl = `${config.casdoor_endpoint}/api/login`
  const signinUrl = 'https://tina-test.bfbdata.com/casdoor/api/login'
  const queryParams = new URLSearchParams({
    clientId: config.client_id,
    responseType: 'code',
    redirectUri: redirectUri,
  }).toString()

  const data = {
    application: config.application,
    organization: config.organization,
    username,
    password,
    autoSignin: true,
    language: '',
    signinMethod: 'Password',
    type: 'code',
  }

  return await fetchPost(`${signinUrl}?${queryParams}`, data)
}

// 3. code 换 token
export async function casdoorExchangeToken(code: string) {
  const url = `${GATEWAY_URL}/api/v1/casdoor/token`
  return fetchPost<{ token: string }>(url, {
    code,
    redirect_uri: redirectUri,
  })
}

// 4. 获取用户信息
export async function casdoorUserInfo(accessToken: string) {
  // 获取动态配置
  const config = await getCasdoorConfig()

  // const userInfoUrl = `${config.casdoor_endpoint}/api/userinfo`
  const userInfoUrl = 'https://tina-test.bfbdata.com/casdoor/api/userinfo'

  return fetchGet(userInfoUrl, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })
}

// 5. 获取指定用户信息
export async function casdoorGetUser(
  accessToken: string,
  userId: string,
): Promise<AuthUser> {
  // 获取动态配置
  const config = await getCasdoorConfig()

  // const getUserUrl = `${config.casdoor_endpoint}/api/get-user`
  const getUserUrl = 'https://tina-test.bfbdata.com/casdoor/api/get-user'
  const queryParams = new URLSearchParams({
    userId,
  }).toString()

  return await fetchGet(`${getUserUrl}?${queryParams}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })
}

/**
 *
 * @param id owner/name
 * @param token
 * @param user
 */
export async function casdoorUpdateUser(
  token: string,
  id: string,
  user: AuthUser,
) {
  // 获取动态配置
  const config = await getCasdoorConfig()

  // const updateUserUrl = `${config.casdoor_endpoint}/api/update-user`
  const updateUserUrl = 'https://tina-test.bfbdata.com/casdoor/api/update-user'
  const queryParams = new URLSearchParams({
    id,
  }).toString()

  const response = await fetch(`${updateUserUrl}?${queryParams}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(user),
    credentials: 'omit',
  })

  return await handleResponse(response)
}
