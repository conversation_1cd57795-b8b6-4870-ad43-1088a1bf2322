import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { App as cap } from '@capacitor/app'
import { Toast } from '@capacitor/toast'
import '@fortawesome/fontawesome-free/css/all.css'
import { setupIonicReact } from '@ionic/react'
import '@ionic/react/css/core.css'
import 'animate.css'
import App from './App.tsx'
import './i18n'
import './index.css'
import './preflight.css'

// 全局标志，用于控制是否需要处理返回键
declare global {
  interface Window {
    shouldHandleBackButton?: boolean
  }
}

// 添加全局蓝色渐变背景样式
document.body.style.background =
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
document.body.style.margin = '0'
document.body.style.padding = '0'
document.body.style.width = '100vw'
document.body.style.height = '100vh'

// 添加隐藏滚动条的样式
const style = document.createElement('style')
style.textContent = `
  body::-webkit-scrollbar {
    display: none;
  }
`
document.head.appendChild(style)
setupIonicReact({
  mode: 'ios', // 使用 iOS 风格
  rippleEffect: false,
  animated: true,
})
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)

// 双击退出
let lastBackButtonPress = 0

cap.addListener('backButton', ({ canGoBack }) => {
  // 如果其他组件正在处理返回键，则不处理
  if (window.shouldHandleBackButton === false) {
    return
  }

  const currentTime = new Date().getTime()

  if (!canGoBack) {
    if (currentTime - lastBackButtonPress < 2000) {
      cap.exitApp()
    } else {
      lastBackButtonPress = currentTime
      Toast.show({
        text: '再按一次退出应用',
      })
    }
  } else {
    console.log("user press back")
    // history.back()
  }
})

// 初始化标志
window.shouldHandleBackButton = true
