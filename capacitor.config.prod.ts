import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'tina.chat',
  appName: '<PERSON> Chat',
  webDir: 'dist',
  // 生产环境中移除服务器配置，使用打包的静态资源
  server: {
    androidScheme: 'http', // 使用更安全的 HTTPS
    cleartext: true // 允许明文通信，确保非HTTPS内容也能加载
  },
  android: {
    buildOptions: {
      keystorePath: 'app/tina-app-key.keystore',
      keystorePassword: 'tinachat2024',
      keystoreAlias: 'tina-app-key',
      keystoreAliasPassword: 'tinachat2024'
    },
    overrideUserAgent: 'Tina Chat Android App',
    appendUserAgent: 'Tina Chat Android App',
    webContentsDebuggingEnabled: true // 生产环境禁用调试
  },
  plugins: {
    CapacitorHttp: {
      enabled: false
    }
  }
};

export default config; 